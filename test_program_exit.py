#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序退出测试工具
测试修复后的程序退出流程是否正常
"""

import os
import sys
import time
import asyncio
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import BrowserPool
from src.core.database import get_database
from src.models.account import Account
from src.utils.logger import get_logger


def test_browser_cleanup():
    """测试浏览器清理功能"""
    print("🧪 测试浏览器清理功能...")
    
    async def async_test():
        # 创建浏览器池
        browser_pool = BrowserPool()
        
        # 创建一个测试账号
        db = get_database()
        test_account = Account(
            id=999,
            username="test_cleanup",
            password="test123",
            email="<EMAIL>",
            platform="x",
            status="active"
        )
        
        try:
            print("   🚀 创建测试WebDriver...")
            # 尝试创建WebDriver
            driver_wrapper = await browser_pool.get_driver(test_account)
            
            if driver_wrapper:
                print("   ✅ WebDriver创建成功")
                
                # 测试清理
                print("   🧹 测试清理功能...")
                start_time = time.time()
                
                await browser_pool.close_all()
                
                end_time = time.time()
                cleanup_time = end_time - start_time
                
                print(f"   ✅ 清理完成！耗时: {cleanup_time:.2f}秒")
                
                if cleanup_time > 5.0:
                    print(f"   ⚠️ 清理时间较长: {cleanup_time:.2f}秒")
                else:
                    print(f"   ✅ 清理时间正常: {cleanup_time:.2f}秒")
                
                return True
            else:
                print("   ❌ WebDriver创建失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    # 运行异步测试
    try:
        return asyncio.run(async_test())
    except Exception as e:
        print(f"   ❌ 异步测试失败: {e}")
        return False


def test_thread_cleanup():
    """测试线程清理功能"""
    print("\n🧪 测试线程清理功能...")
    
    def cleanup_task():
        """模拟清理任务"""
        try:
            # 模拟一个可能卡住的操作
            time.sleep(2)  # 正常情况
            return True
        except Exception as e:
            print(f"清理任务异常: {e}")
            return False
    
    # 测试线程超时机制
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    
    start_time = time.time()
    cleanup_thread.join(timeout=3.0)
    end_time = time.time()
    
    if cleanup_thread.is_alive():
        print(f"   ⚠️ 线程超时，耗时: {end_time - start_time:.2f}秒")
        return False
    else:
        print(f"   ✅ 线程正常完成，耗时: {end_time - start_time:.2f}秒")
        return True


def test_chrome_process_cleanup():
    """测试Chrome进程清理"""
    print("\n🧪 测试Chrome进程清理...")
    
    try:
        import psutil
        import subprocess
        
        # 检查当前Chrome进程
        chrome_processes_before = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'chrome' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if any(indicator in cmdline for indicator in [
                        'browser_profiles', 'account_', 'remote-debugging-port=9'
                    ]):
                        chrome_processes_before.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"   📊 清理前程序Chrome进程: {len(chrome_processes_before)} 个")
        
        # 执行清理
        current_dir = os.getcwd()
        cleaned_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'chrome' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if any(indicator in cmdline for indicator in [
                        'browser_profiles', 'account_', 'remote-debugging-port=9',
                        current_dir.replace('\\', '/')
                    ]):
                        proc.terminate()
                        cleaned_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 等待进程终止
        time.sleep(2)
        
        # 检查清理后的进程
        chrome_processes_after = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'chrome' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if any(indicator in cmdline for indicator in [
                        'browser_profiles', 'account_', 'remote-debugging-port=9'
                    ]):
                        chrome_processes_after.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"   📊 清理了 {cleaned_count} 个Chrome进程")
        print(f"   📊 清理后程序Chrome进程: {len(chrome_processes_after)} 个")
        
        if len(chrome_processes_after) == 0:
            print("   ✅ Chrome进程清理成功")
            return True
        else:
            print("   ⚠️ 仍有Chrome进程残留")
            return False
            
    except ImportError:
        print("   ❌ psutil未安装，跳过Chrome进程测试")
        return True
    except Exception as e:
        print(f"   ❌ Chrome进程清理测试失败: {e}")
        return False


def test_program_exit_simulation():
    """模拟程序退出流程"""
    print("\n🧪 模拟程序退出流程...")
    
    try:
        from src.core.browser_manager import get_browser_pool
        from src.core.task_scheduler import get_task_scheduler
        from src.core.system_monitor import get_system_monitor
        
        start_time = time.time()
        
        # 模拟停止系统监控
        try:
            system_monitor = get_system_monitor()
            system_monitor.stop_monitoring()
            print("   ✅ 系统监控已停止")
        except Exception as e:
            print(f"   ⚠️ 停止系统监控失败: {e}")
        
        # 模拟停止任务调度器
        try:
            task_scheduler = get_task_scheduler()
            task_scheduler.stop()
            print("   ✅ 任务调度器已停止")
        except Exception as e:
            print(f"   ⚠️ 停止任务调度器失败: {e}")
        
        # 模拟清理浏览器池
        try:
            browser_pool = get_browser_pool()
            
            # 使用修复后的清理方法
            def cleanup_task():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    async def cleanup_with_timeout():
                        try:
                            await asyncio.wait_for(browser_pool.close_all(), timeout=3.0)
                        except asyncio.TimeoutError:
                            print("   ⚠️ 浏览器池清理超时")
                        except Exception as e:
                            print(f"   ⚠️ 浏览器池清理异常: {e}")
                    
                    loop.run_until_complete(cleanup_with_timeout())
                    
                except Exception as e:
                    print(f"   ⚠️ 清理线程异常: {e}")
                finally:
                    try:
                        if not loop.is_closed():
                            loop.close()
                    except Exception:
                        pass
            
            cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
            cleanup_thread.start()
            cleanup_thread.join(timeout=2.0)
            
            if cleanup_thread.is_alive():
                print("   ⚠️ 浏览器池清理线程超时")
            else:
                print("   ✅ 浏览器池清理完成")
                
        except Exception as e:
            print(f"   ⚠️ 清理浏览器池失败: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   📊 模拟退出流程总耗时: {total_time:.2f}秒")
        
        if total_time > 10.0:
            print("   ⚠️ 退出时间较长")
            return False
        else:
            print("   ✅ 退出时间正常")
            return True
            
    except Exception as e:
        print(f"   ❌ 模拟退出流程失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 程序退出修复测试工具")
    print("=" * 50)
    
    # 测试结果
    test_results = []
    
    # 1. 测试线程清理
    result1 = test_thread_cleanup()
    test_results.append(("线程清理", result1))
    
    # 2. 测试Chrome进程清理
    result2 = test_chrome_process_cleanup()
    test_results.append(("Chrome进程清理", result2))
    
    # 3. 测试程序退出流程
    result3 = test_program_exit_simulation()
    test_results.append(("程序退出流程", result3))
    
    # 4. 测试浏览器清理（可选，因为可能需要Chrome）
    # result4 = test_browser_cleanup()
    # test_results.append(("浏览器清理", result4))
    
    # 总结
    print("\n📊 测试结果总结:")
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序退出修复成功")
        print("\n💡 建议:")
        print("   1. 重新启动你的自动化程序")
        print("   2. 程序退出时应该不会再卡住")
    else:
        print("⚠️ 部分测试失败，可能仍有问题")
        print("\n💡 建议:")
        print("   1. 检查失败的测试项")
        print("   2. 重启计算机后再试")


if __name__ == "__main__":
    main()
