#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务调度器模块
"""

import asyncio
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import LoggerMixin
from src.config.settings import get_settings


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class ScheduledTask:
    """调度任务数据类"""
    id: str
    name: str
    func: Callable
    args: tuple = ()
    kwargs: dict = None
    priority: TaskPriority = TaskPriority.NORMAL
    scheduled_time: Optional[datetime] = None
    max_retries: int = 3
    retry_count: int = 0
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.created_at is None:
            self.created_at = datetime.now()


class TaskScheduler(LoggerMixin):
    """任务调度器"""
    
    def __init__(self, max_workers: int = None):
        self.settings = get_settings()
        self.max_workers = max_workers or self.settings.max_concurrent_tasks
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 任务队列
        self.pending_tasks: List[ScheduledTask] = []
        self.running_tasks: Dict[str, ScheduledTask] = {}
        self.completed_tasks: Dict[str, ScheduledTask] = {}
        
        # 控制变量
        self.is_running = False
        self.scheduler_thread = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0
        }
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            self.logger.warning("调度器已在运行")
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 等待调度器线程结束
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("任务调度器已停止")
    
    def add_task(self, task: ScheduledTask) -> bool:
        """
        添加任务
        
        Args:
            task: 调度任务
            
        Returns:
            是否添加成功
        """
        try:
            with self.lock:
                # 检查任务ID是否已存在
                if self._task_exists(task.id):
                    self.logger.warning(f"任务ID已存在: {task.id}")
                    return False
                
                # 添加到待处理队列
                self.pending_tasks.append(task)
                
                # 按优先级和调度时间排序
                self.pending_tasks.sort(
                    key=lambda t: (
                        -t.priority.value,  # 优先级高的在前
                        t.scheduled_time or datetime.now()  # 调度时间早的在前
                    )
                )
                
                self.stats['total_tasks'] += 1
                
            self.logger.debug(f"添加任务: {task.name} ({task.id})")
            return True
            
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否取消成功
        """
        try:
            with self.lock:
                # 从待处理队列中移除
                for i, task in enumerate(self.pending_tasks):
                    if task.id == task_id:
                        task.status = TaskStatus.CANCELLED
                        self.pending_tasks.pop(i)
                        self.completed_tasks[task_id] = task
                        self.stats['cancelled_tasks'] += 1
                        self.logger.info(f"取消待处理任务: {task.name}")
                        return True
                
                # 检查是否在运行中
                if task_id in self.running_tasks:
                    task = self.running_tasks[task_id]
                    task.status = TaskStatus.CANCELLED
                    # 注意：正在运行的任务无法立即停止，只能标记为取消
                    self.logger.info(f"标记运行中任务为取消: {task.name}")
                    return True
                
                self.logger.warning(f"未找到任务: {task_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态
        """
        with self.lock:
            # 检查待处理队列
            for task in self.pending_tasks:
                if task.id == task_id:
                    return task.status
            
            # 检查运行中任务
            if task_id in self.running_tasks:
                return self.running_tasks[task_id].status
            
            # 检查已完成任务
            if task_id in self.completed_tasks:
                return self.completed_tasks[task_id].status
            
            return None
    
    def get_task_info(self, task_id: str) -> Optional[ScheduledTask]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象
        """
        with self.lock:
            # 检查待处理队列
            for task in self.pending_tasks:
                if task.id == task_id:
                    return task
            
            # 检查运行中任务
            if task_id in self.running_tasks:
                return self.running_tasks[task_id]
            
            # 检查已完成任务
            if task_id in self.completed_tasks:
                return self.completed_tasks[task_id]
            
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            current_stats = self.stats.copy()
            current_stats.update({
                'pending_tasks': len(self.pending_tasks),
                'running_tasks': len(self.running_tasks),
                'completed_tasks_stored': len(self.completed_tasks)
            })
            
            return current_stats
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.is_running:
            try:
                self._process_pending_tasks()
                self._cleanup_completed_tasks()
                
                # 短暂休眠
                threading.Event().wait(1)
                
            except Exception as e:
                self.logger.error(f"调度器循环异常: {e}")
                threading.Event().wait(5)
    
    def _process_pending_tasks(self):
        """处理待处理任务"""
        with self.lock:
            if not self.pending_tasks:
                return
            
            # 检查是否有可用的工作线程
            if len(self.running_tasks) >= self.max_workers:
                return
            
            current_time = datetime.now()
            
            # 查找可以执行的任务
            tasks_to_execute = []
            for task in self.pending_tasks[:]:
                # 检查调度时间
                if task.scheduled_time and task.scheduled_time > current_time:
                    continue
                
                # 检查是否有足够的工作线程
                if len(self.running_tasks) + len(tasks_to_execute) >= self.max_workers:
                    break
                
                tasks_to_execute.append(task)
                self.pending_tasks.remove(task)
        
        # 执行任务
        for task in tasks_to_execute:
            self._execute_task(task)
    
    def _execute_task(self, task: ScheduledTask):
        """
        执行任务
        
        Args:
            task: 调度任务
        """
        try:
            with self.lock:
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.now()
                self.running_tasks[task.id] = task
            
            # 提交到线程池执行
            future = self.executor.submit(self._run_task, task)
            
            # 设置回调
            future.add_done_callback(lambda f: self._task_completed(task, f))
            
        except Exception as e:
            self.logger.error(f"执行任务失败: {task.name}, {e}")
            self._mark_task_failed(task, str(e))
    
    def _run_task(self, task: ScheduledTask):
        """
        运行任务
        
        Args:
            task: 调度任务
        """
        try:
            # 检查任务是否被取消
            if task.status == TaskStatus.CANCELLED:
                return
            
            # 执行任务函数
            if asyncio.iscoroutinefunction(task.func):
                # 异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(task.func(*task.args, **task.kwargs))
                finally:
                    loop.close()
            else:
                # 同步函数
                result = task.func(*task.args, **task.kwargs)
            
            return result
            
        except Exception as e:
            self.logger.error(f"任务执行异常: {task.name}, {e}")
            raise
    
    def _task_completed(self, task: ScheduledTask, future):
        """
        任务完成回调
        
        Args:
            task: 调度任务
            future: Future对象
        """
        try:
            with self.lock:
                # 从运行中任务移除
                if task.id in self.running_tasks:
                    del self.running_tasks[task.id]
                
                task.completed_at = datetime.now()
                
                # 检查执行结果
                if future.exception():
                    # 任务执行失败
                    error = future.exception()
                    self._handle_task_failure(task, str(error))
                else:
                    # 任务执行成功
                    task.status = TaskStatus.COMPLETED
                    self.completed_tasks[task.id] = task
                    self.stats['completed_tasks'] += 1
                    self.logger.info(f"任务完成: {task.name}")
                
        except Exception as e:
            self.logger.error(f"任务完成回调异常: {e}")
    
    def _handle_task_failure(self, task: ScheduledTask, error_message: str):
        """
        处理任务失败
        
        Args:
            task: 调度任务
            error_message: 错误信息
        """
        task.error_message = error_message
        task.retry_count += 1
        
        # 检查是否需要重试
        if task.retry_count < task.max_retries:
            # 重新加入待处理队列
            task.status = TaskStatus.PENDING
            task.scheduled_time = datetime.now() + timedelta(minutes=task.retry_count * 2)  # 指数退避
            self.pending_tasks.append(task)
            self.logger.warning(f"任务失败，将重试: {task.name} ({task.retry_count}/{task.max_retries})")
        else:
            # 标记为失败
            task.status = TaskStatus.FAILED
            self.completed_tasks[task.id] = task
            self.stats['failed_tasks'] += 1
            self.logger.error(f"任务最终失败: {task.name}")
    
    def _mark_task_failed(self, task: ScheduledTask, error_message: str):
        """
        标记任务失败
        
        Args:
            task: 调度任务
            error_message: 错误信息
        """
        with self.lock:
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            self._handle_task_failure(task, error_message)
    
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            # 保留最近1000个已完成任务
            if len(self.completed_tasks) > 1000:
                # 按完成时间排序，保留最新的
                sorted_tasks = sorted(
                    self.completed_tasks.values(),
                    key=lambda t: t.completed_at or datetime.min,
                    reverse=True
                )
                
                # 保留前1000个
                tasks_to_keep = {task.id: task for task in sorted_tasks[:1000]}
                self.completed_tasks = tasks_to_keep
    
    def _task_exists(self, task_id: str) -> bool:
        """
        检查任务是否存在
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否存在
        """
        # 检查待处理队列
        for task in self.pending_tasks:
            if task.id == task_id:
                return True
        
        # 检查运行中任务
        if task_id in self.running_tasks:
            return True
        
        # 检查已完成任务
        if task_id in self.completed_tasks:
            return True
        
        return False


# 全局任务调度器实例
_task_scheduler = None


def get_task_scheduler() -> TaskScheduler:
    """获取全局任务调度器实例"""
    global _task_scheduler
    if _task_scheduler is None:
        _task_scheduler = TaskScheduler()
    return _task_scheduler
