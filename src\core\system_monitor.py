#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控模块
"""

import psutil
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from src.utils.logger import LoggerMixin
from src.core.operation_logger import get_operation_logger
from src.config.settings import get_settings


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_percent: float
    disk_used: int
    disk_total: int
    network_sent: int
    network_recv: int
    process_count: int
    thread_count: int


class SystemMonitor(LoggerMixin):
    """系统监控器"""
    
    def __init__(self, collect_interval: int = 60):
        """
        初始化系统监控器
        
        Args:
            collect_interval: 数据收集间隔（秒）
        """
        self.collect_interval = collect_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1440  # 保留24小时的数据（每分钟一个点）
        
        self.operation_logger = get_operation_logger()
        self.settings = get_settings()
        
        # 警告阈值
        self.warning_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0
        }
        
        # 上次警告时间（避免频繁警告）
        self.last_warning_time = {}
        self.warning_cooldown = 300  # 5分钟冷却时间
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("系统监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("系统监控已启动")
        self.operation_logger.log_system_operation("启动系统监控", "监控间隔: {}秒".format(self.collect_interval))
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("系统监控已停止")
        self.operation_logger.log_system_operation("停止系统监控")
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = self._collect_metrics()
                
                # 添加到历史记录
                self._add_metrics(metrics)
                
                # 检查警告条件
                self._check_warnings(metrics)
                
                # 等待下次收集
                time.sleep(self.collect_interval)
                
            except Exception as e:
                self.logger.error(f"系统监控异常: {e}")
                time.sleep(self.collect_interval)
    
    def _collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            
            # 网络信息
            network = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            # 线程数（当前进程）
            current_process = psutil.Process()
            thread_count = current_process.num_threads()
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used=memory.used,
                memory_total=memory.total,
                disk_percent=disk.percent,
                disk_used=disk.used,
                disk_total=disk.total,
                network_sent=network.bytes_sent,
                network_recv=network.bytes_recv,
                process_count=process_count,
                thread_count=thread_count
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            # 返回默认值
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used=0,
                memory_total=0,
                disk_percent=0.0,
                disk_used=0,
                disk_total=0,
                network_sent=0,
                network_recv=0,
                process_count=0,
                thread_count=0
            )
    
    def _add_metrics(self, metrics: SystemMetrics):
        """添加指标到历史记录"""
        self.metrics_history.append(metrics)
        
        # 限制历史记录大小
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
    
    def _check_warnings(self, metrics: SystemMetrics):
        """检查警告条件"""
        current_time = datetime.now()
        
        # 检查CPU使用率
        if metrics.cpu_percent > self.warning_thresholds['cpu_percent']:
            if self._should_send_warning('cpu', current_time):
                self.logger.warning(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
                self.operation_logger.log_system_operation(
                    "系统警告", 
                    f"CPU使用率过高: {metrics.cpu_percent:.1f}%",
                    "warning"
                )
        
        # 检查内存使用率
        if metrics.memory_percent > self.warning_thresholds['memory_percent']:
            if self._should_send_warning('memory', current_time):
                self.logger.warning(f"内存使用率过高: {metrics.memory_percent:.1f}%")
                self.operation_logger.log_system_operation(
                    "系统警告", 
                    f"内存使用率过高: {metrics.memory_percent:.1f}%",
                    "warning"
                )
        
        # 检查磁盘使用率
        if metrics.disk_percent > self.warning_thresholds['disk_percent']:
            if self._should_send_warning('disk', current_time):
                self.logger.warning(f"磁盘使用率过高: {metrics.disk_percent:.1f}%")
                self.operation_logger.log_system_operation(
                    "系统警告", 
                    f"磁盘使用率过高: {metrics.disk_percent:.1f}%",
                    "warning"
                )
    
    def _should_send_warning(self, warning_type: str, current_time: datetime) -> bool:
        """判断是否应该发送警告"""
        last_time = self.last_warning_time.get(warning_type)
        
        if not last_time:
            self.last_warning_time[warning_type] = current_time
            return True
        
        if (current_time - last_time).total_seconds() > self.warning_cooldown:
            self.last_warning_time[warning_type] = current_time
            return True
        
        return False
    
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """获取当前系统指标"""
        if not self.metrics_history:
            return self._collect_metrics()
        
        return self.metrics_history[-1]
    
    def get_metrics_history(self, hours: int = 1) -> List[SystemMetrics]:
        """
        获取指定时间范围内的指标历史
        
        Args:
            hours: 小时数
            
        Returns:
            指标历史列表
        """
        if not self.metrics_history:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            metrics for metrics in self.metrics_history
            if metrics.timestamp >= cutoff_time
        ]
    
    def get_system_summary(self) -> Dict[str, Any]:
        """获取系统摘要信息"""
        try:
            current_metrics = self.get_current_metrics()
            
            if not current_metrics:
                return {}
            
            # 获取最近1小时的指标
            recent_metrics = self.get_metrics_history(1)
            
            # 计算平均值
            if recent_metrics:
                avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
                avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            else:
                avg_cpu = current_metrics.cpu_percent
                avg_memory = current_metrics.memory_percent
            
            summary = {
                'current': {
                    'cpu_percent': current_metrics.cpu_percent,
                    'memory_percent': current_metrics.memory_percent,
                    'disk_percent': current_metrics.disk_percent,
                    'process_count': current_metrics.process_count,
                    'thread_count': current_metrics.thread_count
                },
                'average_1h': {
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory
                },
                'memory': {
                    'used_gb': current_metrics.memory_used / (1024**3),
                    'total_gb': current_metrics.memory_total / (1024**3)
                },
                'disk': {
                    'used_gb': current_metrics.disk_used / (1024**3),
                    'total_gb': current_metrics.disk_total / (1024**3)
                },
                'network': {
                    'sent_mb': current_metrics.network_sent / (1024**2),
                    'recv_mb': current_metrics.network_recv / (1024**2)
                },
                'monitoring_status': self.is_monitoring,
                'last_update': current_metrics.timestamp
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取系统摘要失败: {e}")
            return {}
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取性能报告
        
        Args:
            hours: 报告时间范围（小时）
            
        Returns:
            性能报告
        """
        try:
            metrics_list = self.get_metrics_history(hours)
            
            if not metrics_list:
                return {}
            
            # 计算统计信息
            cpu_values = [m.cpu_percent for m in metrics_list]
            memory_values = [m.memory_percent for m in metrics_list]
            
            report = {
                'time_range_hours': hours,
                'data_points': len(metrics_list),
                'cpu': {
                    'min': min(cpu_values),
                    'max': max(cpu_values),
                    'avg': sum(cpu_values) / len(cpu_values),
                    'current': cpu_values[-1] if cpu_values else 0
                },
                'memory': {
                    'min': min(memory_values),
                    'max': max(memory_values),
                    'avg': sum(memory_values) / len(memory_values),
                    'current': memory_values[-1] if memory_values else 0
                },
                'warnings': {
                    'cpu_high_count': sum(1 for v in cpu_values if v > self.warning_thresholds['cpu_percent']),
                    'memory_high_count': sum(1 for v in memory_values if v > self.warning_thresholds['memory_percent'])
                },
                'generated_at': datetime.now()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return {}
    
    def cleanup_old_metrics(self, hours: int = 48):
        """
        清理旧的指标数据
        
        Args:
            hours: 保留小时数
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            original_count = len(self.metrics_history)
            self.metrics_history = [
                metrics for metrics in self.metrics_history
                if metrics.timestamp >= cutoff_time
            ]
            
            cleaned_count = original_count - len(self.metrics_history)
            
            if cleaned_count > 0:
                self.logger.info(f"清理旧指标数据: {cleaned_count}条")
                
        except Exception as e:
            self.logger.error(f"清理旧指标数据失败: {e}")


# 全局系统监控器实例
_system_monitor = None


def get_system_monitor() -> SystemMonitor:
    """获取全局系统监控器实例"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor
