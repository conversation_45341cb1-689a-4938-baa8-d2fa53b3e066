#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome启动卡住问题修复工具
专门解决Chrome启动时卡住不动的问题
"""

import os
import sys
import time
import psutil
import subprocess
import shutil
from pathlib import Path


def check_chrome_processes():
    """检查Chrome进程"""
    print("🔍 检查Chrome进程...")
    
    chrome_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'cmdline': cmdline,
                    'create_time': proc.info['create_time']
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"   📊 发现 {len(chrome_processes)} 个Chrome进程")
    
    # 分类显示
    program_chrome = []
    personal_chrome = []
    
    for proc in chrome_processes:
        if any(indicator in proc['cmdline'] for indicator in [
            'browser_profiles', 'account_', 'remote-debugging-port=9'
        ]):
            program_chrome.append(proc)
        else:
            personal_chrome.append(proc)
    
    print(f"   🤖 程序Chrome进程: {len(program_chrome)} 个")
    print(f"   👤 个人Chrome进程: {len(personal_chrome)} 个")
    
    return program_chrome, personal_chrome


def check_ports():
    """检查端口占用"""
    print("\n🔍 检查端口占用...")
    
    ports_to_check = range(9000, 9020)  # 检查常用的调试端口
    occupied_ports = []
    
    for port in ports_to_check:
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == 'LISTEN':
                try:
                    proc = psutil.Process(conn.pid) if conn.pid else None
                    proc_name = proc.name() if proc else "Unknown"
                    occupied_ports.append({
                        'port': port,
                        'pid': conn.pid,
                        'process': proc_name
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    occupied_ports.append({
                        'port': port,
                        'pid': conn.pid,
                        'process': "Unknown"
                    })
                break
    
    print(f"   📊 发现 {len(occupied_ports)} 个占用的端口")
    for port_info in occupied_ports:
        print(f"   🔒 端口 {port_info['port']}: PID {port_info['pid']} ({port_info['process']})")
    
    return occupied_ports


def check_user_data_dir():
    """检查用户数据目录"""
    print("\n🔍 检查用户数据目录...")
    
    user_data_dir = Path("data/browser_profiles/account_10")
    
    if not user_data_dir.exists():
        print("   ✅ 用户数据目录不存在，无需检查")
        return []
    
    # 检查锁文件
    lock_files = []
    lock_patterns = ["*LOCK*", "*.lock", "*SingletonLock*", "*SingletonSocket*"]
    
    for pattern in lock_patterns:
        lock_files.extend(list(user_data_dir.rglob(pattern)))
    
    print(f"   📊 发现 {len(lock_files)} 个锁文件")
    for lock_file in lock_files:
        print(f"   🔒 锁文件: {lock_file.relative_to(user_data_dir)}")
    
    return lock_files


def emergency_cleanup():
    """紧急清理"""
    print("\n🚨 执行紧急清理...")
    
    cleaned_items = 0
    
    # 1. 清理程序Chrome进程
    print("   1️⃣ 清理程序Chrome进程...")
    program_chrome, _ = check_chrome_processes()
    
    for proc_info in program_chrome:
        try:
            proc = psutil.Process(proc_info['pid'])
            proc.terminate()
            print(f"      ✅ 终止进程: PID {proc_info['pid']}")
            cleaned_items += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            print(f"      ❌ 无法终止进程: PID {proc_info['pid']}")
    
    # 等待进程终止
    time.sleep(3)
    
    # 强制杀死残留进程
    remaining_program_chrome, _ = check_chrome_processes()
    if remaining_program_chrome:
        print("   🔨 强制杀死残留程序Chrome进程...")
        for proc_info in remaining_program_chrome:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.kill()
                print(f"      ✅ 强制杀死: PID {proc_info['pid']}")
                cleaned_items += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print(f"      ❌ 无法杀死进程: PID {proc_info['pid']}")
    
    # 2. 清理端口占用
    print("   2️⃣ 清理端口占用...")
    occupied_ports = check_ports()
    
    for port_info in occupied_ports:
        if 'chrome' in port_info['process'].lower():
            try:
                proc = psutil.Process(port_info['pid'])
                proc.terminate()
                print(f"      ✅ 释放端口 {port_info['port']}: PID {port_info['pid']}")
                cleaned_items += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print(f"      ❌ 无法释放端口 {port_info['port']}: PID {port_info['pid']}")
    
    # 3. 清理锁文件
    print("   3️⃣ 清理锁文件...")
    lock_files = check_user_data_dir()
    
    for lock_file in lock_files:
        try:
            lock_file.unlink()
            print(f"      ✅ 删除锁文件: {lock_file.name}")
            cleaned_items += 1
        except Exception as e:
            print(f"      ❌ 无法删除锁文件 {lock_file.name}: {e}")
    
    # 4. 清理临时文件
    print("   4️⃣ 清理Chrome临时文件...")
    temp_dirs = [
        Path(os.path.expanduser("~/AppData/Local/Temp")),
        Path("temp"),
        Path("data/browser_profiles")
    ]
    
    for temp_dir in temp_dirs:
        if temp_dir.exists():
            try:
                for item in temp_dir.iterdir():
                    if item.is_file() and any(pattern in item.name.lower() for pattern in ['chrome', 'webdriver', 'selenium']):
                        try:
                            item.unlink()
                            cleaned_items += 1
                        except Exception:
                            continue
                    elif item.is_dir() and any(pattern in item.name.lower() for pattern in ['chrome_', 'scoped_dir']):
                        try:
                            shutil.rmtree(item)
                            cleaned_items += 1
                        except Exception:
                            continue
            except Exception as e:
                print(f"      ⚠️ 清理 {temp_dir} 时出错: {e}")
    
    print(f"   📊 总共清理了 {cleaned_items} 个项目")
    return cleaned_items


def test_chrome_startup():
    """测试Chrome启动"""
    print("\n🧪 测试Chrome启动...")
    
    try:
        import undetected_chromedriver as uc
        from selenium.webdriver.chrome.options import Options
        
        # 创建最简单的测试配置
        options = uc.ChromeOptions()
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=9999')
        
        # 使用临时目录
        temp_dir = Path("temp/chrome_startup_test")
        temp_dir.mkdir(parents=True, exist_ok=True)
        options.add_argument(f'--user-data-dir={temp_dir}')
        
        print("   🚀 启动Chrome...")
        start_time = time.time()
        
        driver = uc.Chrome(
            options=options,
            version_main=None,
            suppress_welcome=True,
            use_subprocess=False,
            debug=False
        )
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        print(f"   ✅ Chrome启动成功！耗时: {startup_time:.2f}秒")
        
        # 简单测试
        driver.get("https://www.google.com")
        title = driver.title
        print(f"   ✅ 页面加载成功: {title}")
        
        driver.quit()
        
        # 清理测试目录
        try:
            shutil.rmtree(temp_dir)
        except Exception:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ Chrome启动测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 Chrome启动卡住问题修复工具")
    print("=" * 50)
    
    # 步骤1: 诊断当前状态
    print("📋 步骤1: 诊断当前状态")
    program_chrome, personal_chrome = check_chrome_processes()
    occupied_ports = check_ports()
    lock_files = check_user_data_dir()
    
    # 步骤2: 判断是否需要清理
    need_cleanup = len(program_chrome) > 0 or len(occupied_ports) > 0 or len(lock_files) > 0
    
    if need_cleanup:
        print(f"\n⚠️ 发现问题:")
        if program_chrome:
            print(f"   - {len(program_chrome)} 个程序Chrome进程")
        if occupied_ports:
            print(f"   - {len(occupied_ports)} 个占用的端口")
        if lock_files:
            print(f"   - {len(lock_files)} 个锁文件")
        
        print("\n📋 步骤2: 执行清理")
        cleaned_items = emergency_cleanup()
        
        if cleaned_items > 0:
            print(f"\n✅ 清理完成，等待3秒...")
            time.sleep(3)
        
    else:
        print("\n✅ 环境干净，无需清理")
    
    # 步骤3: 测试Chrome启动
    print("\n📋 步骤3: 测试Chrome启动")
    success = test_chrome_startup()
    
    # 步骤4: 给出建议
    print("\n📋 步骤4: 建议")
    if success:
        print("✅ Chrome启动正常！")
        print("💡 建议:")
        print("   1. 重新启动你的自动化程序")
        print("   2. 如果问题再次出现，可以再次运行此工具")
    else:
        print("❌ Chrome启动仍有问题！")
        print("💡 建议:")
        print("   1. 重启计算机")
        print("   2. 检查Chrome是否需要更新")
        print("   3. 临时关闭杀毒软件测试")
        print("   4. 检查系统资源是否充足")


if __name__ == "__main__":
    main()
